<template>
  <div class="device-selector">
    <div v-if="deviceList.length > 0" class="custom-select" ref="target">
      <div class="selected-option" @click="show = !show">
        <span class="text-[14px] truncate ml-[15px]">
          {{ selectedDevice ? getDeviceName(selectedDevice) : placeholder }}
          <span v-if="selectedDevice" :style="{ color: getDeviceStatus(selectedDevice) === '1' ? '#67C23A' : '#F56C6C', marginLeft: '8px' }">
            {{ getDeviceStatus(selectedDevice) === '1' ? '在线' : '离线' }}
          </span>
        </span>
        <div class="reset-icon" @click.stop="resetSelect" v-if="selectedDevice">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
      <transition name="dropdown" v-show="show">
        <ul class="options-list">
          <li
            v-for="device in deviceList"
            :key="getDeviceId(device)"
            :class="['option', selectedId === getDeviceId(device) && 'active']"
            @click="selectDevice(device)"
          >
            <div class="device-option">
              <span>{{ getDeviceName(device) }}</span>
              <span :style="{ color: getDeviceStatus(device) === '1' ? '#67C23A' : '#F56C6C' }">
                {{ getDeviceStatus(device) === '1' ? '在线' : '离线' }}
              </span>
            </div>
          </li>
        </ul>
      </transition>
    </div>
    <div v-else class="no-device-tip">{{ emptyTip }}</div>
  </div>
</template>

<script setup>
import { getCurrentInstance, ref, watch, computed } from 'vue';
import { onClickOutside } from '@vueuse/core';

const props = defineProps({
  // 设备/摄像头列表
  devices: {
    type: Array,
    default: () => []
  },
  // 当前选中的设备/摄像头ID
  modelValue: {
    type: String,
    default: ''
  },
  // 选择器提示文本
  placeholder: {
    type: String,
    default: '选择设备'
  },
  // ID字段名
  idField: {
    type: String,
    default: 'deviceId'
  },
  // 名称字段名
  nameField: {
    type: String,
    default: 'deviceName'
  },
  // 状态字段名
  statusField: {
    type: String,
    default: 'deviceStatus'
  },
  // 空数据提示
  emptyTip: {
    type: String,
    default: '暂无可用设备'
  }
});

const emit = defineEmits(['update:modelValue', 'change', 'select']);

const { proxy } = getCurrentInstance();

// 设备列表
const deviceList = computed(() => props.devices || []);

// 选中的设备ID
const selectedId = ref(props.modelValue);

// 下拉框显示状态
const show = ref(false);

// 目标元素引用
const target = ref(null);

// 点击外部关闭下拉框
onClickOutside(target, () => {
  show.value = false;
});

// 当前选中的设备对象
const selectedDevice = computed(() => {
  return deviceList.value.find(device => getDeviceId(device) === selectedId.value);
});

// 监听外部modelValue变化
watch(() => props.modelValue, (newVal) => {
  selectedId.value = newVal;
});

// 监听内部选择变化
watch(() => selectedId.value, (newVal) => {
  emit('update:modelValue', newVal);
  // 查找当前选中的设备对象
  const selectedDevice = deviceList.value.find(device => getDeviceId(device) === newVal);
  // 发送选中的设备对象
  emit('change', selectedDevice);
  emit('select', selectedDevice);
});

// 获取设备ID
const getDeviceId = (device) => {
  return device[props.idField] || '';
};

// 获取设备名称
const getDeviceName = (device) => {
  return device[props.nameField] || '未命名设备';
};

// 获取设备状态
const getDeviceStatus = (device) => {
  return device[props.statusField] || '0';
};

// 选择设备
const selectDevice = (device) => {
  selectedId.value = getDeviceId(device);
  show.value = false;
};

// 重置选择
const resetSelect = () => {
  selectedId.value = '';
  show.value = false;
};
</script>

<style lang="scss" scoped>
.device-selector {
  width: 200px !important; // 减小整体宽度
  
  .custom-select {
    width: 200px !important; // 减小宽度
    height: 45px; // 减小高度
    font-size: 14px; // 减小字体
    background: #395373;
    box-shadow: 0px -1px 2px 0px rgba(0,0,0,0.5);
    position: relative;
    
    .selected-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 45px; // 减小高度
      line-height: 45px; // 调整行高
      cursor: pointer;
      
      .reset-icon {
        position: absolute;
        right: 10px; // 减小右边距
        top: 50%;
        transform: translateY(-50%);
        width: 20px; // 减小图标尺寸
        height: 20px; // 减小图标尺寸
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #fff;
        opacity: 0.7;
        transition: all 0.3s ease;
        border-radius: 50%;
        
        &:hover {
          opacity: 1;
          background: rgba(255, 255, 255, 0.1);
          color: #ff6b6b;
        }
        
        svg {
          width: 14px; // 减小SVG尺寸
          height: 14px; // 减小SVG尺寸
        }
      }
      
      >span {
        flex-grow: 1;
        margin-right: 35px; // 减小右边距
        color: white;
      }
    }
    
    .options-list {
      width: 280px !important; // 减小下拉列表宽度
      max-height: 250px; // 减小最大高度
      overflow-y: auto;
      position: absolute;
      top: 45px; // 调整顶部位置
      left: 0;
      background: #2D4159;
      box-shadow: 0px 2px 10px 3px rgba(0,0,0,0.2), 0px 5px 10px 0px rgba(255,255,255,0.05);
      border-radius: 6px;
      border-top: none;
      padding: 8px; // 减小内边距
      margin: 0;
      box-sizing: border-box;
      z-index: 200;
      color: white;
      transition: all 0.3s ease-in-out;
    }
    
    .option {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: left;
      cursor: pointer;
      font-size: 14px; // 减小字体
      color: white;
      box-sizing: border-box;
      padding: 8px 10px 8px 15px; // 减小内边距
      text-align: left;
      margin-bottom: 6px; // 减小底部间距
      white-space: nowrap;
      
      .device-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }
    }
    
    .option:hover {
      background: linear-gradient(0deg, #2D4159, #334A66);
      box-shadow: 0px 5px 9px 1px rgba(0,0,0,0.3) inset, 0px -2px 4px 2px rgba(255,255,255,0.08) inset;
      border-radius: 4px;
      color: #33BBFF;
      font-weight: bold;
    }
    
    .active {
      background: linear-gradient(0deg, #2D4159, #334A66);
      box-shadow: 0px 5px 9px 1px rgba(0,0,0,0.3) inset, 0px -2px 4px 2px rgba(255,255,255,0.08) inset;
      border-radius: 4px;
      color: #33BBFF;
      font-weight: bold;
    }
  }
  
  .no-device-tip {
    width: 200px !important; // 减小宽度
    height: 45px; // 减小高度
    font-size: 14px; // 减小字体
    background: #395373;
    box-shadow: 0px -1px 2px 0px rgba(0,0,0,0.5);
    color: white;
    display: flex;
    align-items: center;
    padding: 0px 15px; // 减小内边距
    box-sizing: border-box;
  }
}

/* 自定义滚动条样式 */
.options-list::-webkit-scrollbar {
  width: 6px;
}

.options-list::-webkit-scrollbar-thumb {
  background-color: #33BBFF;
  border-radius: 3px;
  box-shadow: 1px 0px 2px 0px rgba(255,255,255,0.5);
}

.options-list::-webkit-scrollbar-track {
  background: #27384D;
  box-shadow: 1px 0px 2px 0px rgba(0,0,0,0.3);
  border-radius: 3px;
  box-sizing: border-box;
  padding: 10px 0;
}

/* 响应式调整 */
@media (min-aspect-ratio: 16/9) {
  .device-selector {
    width: 220px !important; // 减小响应式宽度
    
    .custom-select {
      width: 220px !important; // 减小响应式宽度
      
      .selected-option {
        >span {
          margin-right: 40px; // 减小右边距
        }
        
        .reset-icon {
          right: 12px; // 减小右边距
          width: 22px; // 减小图标尺寸
          height: 22px; // 减小图标尺寸
          
          svg {
            width: 16px; // 减小SVG尺寸
            height: 16px; // 减小SVG尺寸
          }
        }
      }
      
      .options-list {
        width: 300px !important; // 减小下拉列表宽度
      }
    }
    
    .no-device-tip {
      width: 220px !important; // 减小提示文本宽度
    }
  }
}

/* 下拉动画 */
.dropdown-enter-active, .dropdown-leave-active {
  transition: all 0.3s ease-in-out;
}

.dropdown-enter-from, .dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>