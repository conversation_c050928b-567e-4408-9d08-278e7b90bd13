<template>
  <div class="left-content">
    <!-- 第一行：项目标题 -->
    <div class="left-item one">
      <!-- <div class="project-title">
        {{ title }}
      </div> -->

      <!-- 第二行：选项卡 -->
      <div class="tabs">
        <div v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: activeTab === index }"
          @click="activeTab = index">
          {{ tab }}
        </div>
      </div>

      <!-- 第三行：内容区域 -->
      <div class="tab-content">
        <!-- 选项卡1：常规检测 -->
        <div v-if="activeTab === 0" class="content-item">
          <!-- 视频监控部分 -->
          <div class="monitor-section">
            <div class="info-section">
              <div class="info-section-bg">{{ t('projectDetail.equipmentFacilities.videoMonitoring') }}</div>
            </div>
            <div class="monitor-control">
              <div class="selector">
                <DeviceSelector
                  v-if="monitorList.length > 0"
                  v-model="selectedMonitorId"
                  :devices="monitorList"
                  placeholder="选择摄像头"
                  id-field="cameraId"
                  name-field="cameraName"
                  status-field="cameraStatus"
                  :disabled="isSwitchingCamera"
                />
                <div v-else class="select-device-hint">暂无可用摄像头</div>
              </div>
              <div class="control-icons">
                <div class="icon-item" @click="toggleFullScreen"
                  :style="{ background: `url(${getImg('map/controlBg.png')}) no-repeat`, backgroundSize: '100% 100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }">
                  <svg-icon name="fullscreen" color="#fff" style="width: 28px; height: 28px; display: block;" />
                </div>
              </div>
            </div>
            <div class="video-container" ref="videoBox">
              <!-- 视频状态指示器 -->
              <div v-if="currentMonitor" class="video-status-indicator">
                <span :class="{'online': currentMonitor.cameraStatus === '1'}">
                  {{ currentMonitor.cameraName || currentMonitor.storeName || '未命名摄像头' }} - {{ currentMonitor.cameraStatus === '1' ? '在线' : '离线' }}
                </span>
              </div>
              
              <!-- 萤石云视频播放器 -->
              <div class="video-player">
                <!-- 为每个摄像头预创建容器，使用v-show控制显示 -->
                <template v-if="monitorList.length > 0">
                  <div
                    v-for="monitor in monitorList"
                    :key="`container-${monitor.cameraId}`"
                    v-show="selectedMonitorId === monitor.cameraId && monitor.cameraStatus === '1'"
                    class="player-wrapper"
                  >
                    <!-- 加载状态 -->
                    <div v-if="localPlayerManager.getLoadingState(getPlayerId(monitor))" class="player-loading-overlay">
                      <div class="loading-spinner-small"></div>
                      <div class="loading-text-small">正在连接监控...</div>
                    </div>
                    <!-- 错误状态 -->
                    <div v-else-if="localPlayerManager.getErrorState(getPlayerId(monitor))" class="player-error-overlay">
                      <div class="error-icon">⚠️</div>
                      <div class="error-text-small">连接失败</div>
                    </div>
                    <!-- 播放器容器 -->
                    <div
                      :id="getPlayerId(monitor)"
                      class="monitor-player"
                    ></div>
                  </div>
                </template>
                
                <!-- 占位文本 -->
                <div v-show="!currentMonitor || currentMonitor.cameraStatus !== '1'" class="video-placeholder">
                  {{ videoPlaceholderText }}
                </div>
                
                <!-- 切换中状态 -->
                <div v-if="isSwitchingCamera" class="player-switching-overlay">
                  <div class="loading-spinner-small"></div>
                  <div class="loading-text-small">正在切换摄像头...</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 扬尘监控部分 -->
          <div class="dust-section">
            <div class="info-section">
              <div class="info-section-bg">{{ t('projectDetail.equipmentFacilities.dustMonitoring.title') }}</div>
            </div>
            <div class="dust-data">
              <div class="dust-row">
                <div class="dust-item" v-for="(item, index) in dustData.slice(0, 4)" :key="index">
                  <div class="dust-value">{{ item.value }}{{ item.unit }}</div>
                  <div class="dust-name">{{ item.name }}</div>
                </div>
              </div>
              <div class="dust-row" style="margin-top: -20px;">
                <div class="dust-item" v-for="(item, index) in dustData.slice(4, 8)" :key="index">
                  <div class="dust-value">{{ item.value }}{{ item.unit }}</div>
                  <div class="dust-name">{{ item.name }}</div>
                </div>
              </div>
            </div>
            <!-- <div class="dust-chart-bg"></div> -->
          </div>
        </div>

        <!-- 选项卡2：塔吊监测 -->
        <div v-if="activeTab === 1" class="content-item">
          <!-- 第一行：三个图标数据展示 -->
          <div class="tower-stats">
            <div class="tower-stat-item-1">
              <div class="stat-number">{{ towerCraneStats.tdCount || 0 }} <span class="stat-string">{{
                t('projectDetail.equipmentFacilities.units.count') }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.towerCrane.totalCount') }}</div>
            </div>
            <div class="tower-stat-item-2">
              <div class="stat-number">{{ towerCraneStats.tdStatusCount || 0 }} <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
              }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.towerCrane.running') }}</div>
            </div>
            <div class="tower-stat-item-3">
              <div class="stat-number">{{ towerCraneStats.offStatusCount || 0 }} <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
              }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.towerCrane.stopped') }}</div>
            </div>
          </div>

          <!-- 第二行：左右两个数据展示 -->
          <div class="tower-details">
            <!-- 左侧：塔吊参数 -->
            <div class="tower-params">
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.towerCrane.height')
                  }}：</span>{{ selectedTowerCraneInfo?.deviceAccessVo?.towerHeight || '--' }}{{ t('projectDetail.equipmentFacilities.units.meter') }}</div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.towerCrane.jibLength')
                  }}：</span>{{ selectedTowerCraneInfo?.deviceAccessVo?.boomLength || '--' }}{{ t('projectDetail.equipmentFacilities.units.meter') }}</div>
              <div class="param-item"><span class="title">{{
                t('projectDetail.equipmentFacilities.towerCrane.counterJibLength') }}：</span>{{ selectedTowerCraneInfo?.deviceAccessVo?.balanceArmLength || '--' }}{{
                    t('projectDetail.equipmentFacilities.units.meter') }}</div>
            </div>

            <!-- 右侧：安全数据 -->
            <div class="tower-safety"></div>
          </div>
        </div>

        <!-- 选项卡3：升降机监测 -->
        <div v-if="activeTab === 2" class="content-item">
          <!-- 第一行：四个图标数据展示 -->
          <div class="tower-stats">
            <div class="tower-stat-item-1">
              <div class="stat-number">{{ elevatorStats.tdCount || 0 }} <span class="stat-string">{{
                t('projectDetail.equipmentFacilities.units.count') }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.totalCount') }}</div>
            </div>
            <div class="tower-stat-item-2">
              <div class="stat-number">{{ elevatorStats.tdStatusCount || 0 }} <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.onlineCount') }}</div>
            </div>
            <div class="tower-stat-item-3">
              <div class="stat-number">{{ elevatorStats.todayWarnCount || 0 }} <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.todayWarnings') }}</div>
            </div>
            <div class="tower-stat-item-4">
              <div class="stat-number">{{ elevatorStats.todayAlarmCount || 0 }} <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.todayAlarms') }}</div>
            </div>
          </div>

          <!-- 第二行：左右两个数据展示 -->
          <div class="tower-details">
            <div class="tower-params">
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.elevator.weight')
                  }}：</span>{{ selectedElevatorInfo?.deviceAccessVo?.weight || '--' }}{{ t('projectDetail.equipmentFacilities.units.kg') }}</div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.elevator.height')
                  }}：</span>{{ selectedElevatorInfo?.deviceAccessVo?.height || '--' }}{{ t('projectDetail.equipmentFacilities.units.meter') }}</div>
              <div class="param-item"> <span class="title">{{ t('projectDetail.equipmentFacilities.elevator.xTilt')
                  }}：</span> {{ selectedElevatorInfo?.deviceAccessVo?.xTilt || '--' }}</div>
              <div class="param-item"> <span class="title">{{ t('projectDetail.equipmentFacilities.elevator.yTilt')
                  }}：</span>{{ selectedElevatorInfo?.deviceAccessVo?.yTilt || '--' }}</div>
            </div>

            <!-- 右侧：安全数据 -->
            <div class="tower-safety-lift"></div>
          </div>
        </div>

        <!-- 选项卡4：卸料平台 -->
        <div v-if="activeTab === 3" class="content-item">
          <!-- 第一行：三个图标数据展示 -->
          <div class="tower-stats">
            <div class="tower-stat-item-1">
              <div class="stat-number">{{ unloadingStats.tdCount || 0 }} <span class="stat-string">{{
                t('projectDetail.equipmentFacilities.units.count') }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.totalCount') }}</div>
            </div>
            <div class="tower-stat-item-2">
              <div class="stat-number">{{ unloadingStats.tdStatusCount || 0 }} <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.onlineCount') }}</div>
            </div>
            <div class="tower-stat-item-5">
              <div class="stat-number">{{ unloadingStats.offStatusCount || 0 }} <span class="stat-string">{{ t('projectDetail.equipmentFacilities.units.count')
                  }}</span></div>
              <div class="stat-title">{{ t('projectDetail.equipmentFacilities.elevator.offlineCount') }}</div>
            </div>
          </div>

          <!-- 第二行：左右两个数据展示 -->
          <div class="tower-details">
            <div class="tower-params">
              <div class="param-item"><span class="title">{{
                t('projectDetail.equipmentFacilities.unloading.realTimeWeight') }} ：</span> <span
                  class="title-num">{{ selectedUnloadingInfo?.deviceAccessVo?.realTimeWeight || '--' }}</span>{{ t('projectDetail.equipmentFacilities.units.kg') }}</div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.elevator.xTilt')
                  }}：</span><span class="title-num">{{ selectedUnloadingInfo?.deviceAccessVo?.xTilt || '--' }}{{ t('projectDetail.equipmentFacilities.units.degree') }}</span>
              </div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.elevator.yTilt')
                  }}：</span><span class="title-num">{{ selectedUnloadingInfo?.deviceAccessVo?.yTilt || '--' }}{{ t('projectDetail.equipmentFacilities.units.degree') }}</span>
              </div>
              <div class="param-item"><span class="title">{{
                t('projectDetail.equipmentFacilities.unloading.warningValue') }}：</span><span class="title-num"
                  style="color: #FCD494;">{{ selectedUnloadingInfo?.deviceAccessVo?.warningValue || '--' }}</span>{{ t('projectDetail.equipmentFacilities.units.kg') }}</div>
              <div class="param-item"><span class="title">{{ t('projectDetail.equipmentFacilities.unloading.alarmValue')
                  }}：</span> <span class="title-num" style="color: #FF6262;">{{ selectedUnloadingInfo?.deviceAccessVo?.alarmValue || '--' }}</span>{{
                    t('projectDetail.equipmentFacilities.units.kg') }}</div>
            </div>

            <!-- 右侧：安全数据 -->
            <div class="tower-safety-discharge"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, computed, onUnmounted, onBeforeUnmount } from "vue";
import { useI18n } from 'vue-i18n';
import { getImg } from "@/utils/method";
import request from "@/utils/request";
import DeviceSelector from "@/components/DeviceSelector.vue";
import tokenManager from '@/utils/tokenManagers';
import {
  safeDestroyEZUIKit,
  safeInitEZUIKit,
  setupEZUIKitVisibilityHandler,
  cleanupEZUIKitResources
} from '@/utils/ezuikitFix';

const { t, locale } = useI18n();

const props = defineProps({
  projectData: {
    type: Object,
    default: () => ({}),
  },
});

// ========== 改进的本地播放器管理器 ==========
class LocalPlayerManager {
  constructor() {
    this.players = new Map();
    this.visibilityHandlers = new Map();
    this.loadingStates = new Map();
    this.errorStates = new Map();
    this.initializingPlayers = new Set();
    this.destroyingPlayers = new Set();

    // 单个监控的EZUIKit主题配置
    this.ezuikitTheme = {
      autoFocus: 5,
      poster: "https://resource.eziot.com/group1/M00/00/89/CtwQEmLl8r-AZU7wAAETKlvgerU237.png",
      header: {
        color: "#1890ff",
        activeColor: "#FFFFFF",
        backgroundColor: "#000000",
        btnList: []
      },
      footer: {
        color: "#FFFFFF",
        activeColor: "#1890FF",
        backgroundColor: "#00000051",
        btnList: [
          {
            iconId: "play",
            part: "left",
            defaultActive: 1,
            memo: "播放",
            isrender: 1,
          },
          {
            iconId: "capturePicture",
            part: "left",
            defaultActive: 0,
            memo: "截屏按钮",
            isrender: 1,
          },
          {
            iconId: "sound",
            part: "left",
            defaultActive: 0,
            memo: "声音按钮",
            isrender: 1,
          },
          {
            iconId: "recordvideo",
            part: "left",
            defaultActive: 0,
            memo: "录制按钮",
            isrender: 1,
          },
          {
            iconId: "expend",
            part: "left",
            defaultActive: 0,
            memo: "全局全屏按钮",
            isrender: 1,
          },
        ],
      },
    };
  }

  // 安全地初始化播放器
  async initPlayer({ data, type, onLoadingChange, onError }) {
    if (type !== 'monitor') {
      console.warn('[LocalPlayerManager] 当前只支持monitor类型播放器');
      return;
    }

    if (data.cameraStatus !== '1') {
      console.log(`[LocalPlayerManager] 设备 ${data.nvrSerialNo || data.cameraSn} 通道 ${data.cameraChannel} 不在线`);
      return;
    }

    const deviceSerial = data.nvrSerialNo || data.cameraSn;
    const channelNo = data.cameraChannel || '1';
    const playerId = `monitor-player-${deviceSerial}-${channelNo}`;
    
    // 防重复初始化
    if (this.initializingPlayers.has(playerId)) {
      console.log(`[LocalPlayerManager] 播放器 ${playerId} 正在初始化中`);
      return;
    }
    
    // 如果正在销毁，等待销毁完成
    if (this.destroyingPlayers.has(playerId)) {
      console.log(`[LocalPlayerManager] 等待播放器 ${playerId} 销毁完成`);
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (!this.destroyingPlayers.has(playerId)) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 100);
      });
    }

    this.initializingPlayers.add(playerId);

    try {
      this.setLoadingState(playerId, true);
      this.clearErrorState(playerId);
      if (onLoadingChange) onLoadingChange(playerId, true);

      const playerContainer = document.getElementById(playerId);
      if (!playerContainer) {
        throw new Error(`播放器容器 ${playerId} 不存在`);
      }

      // 清空容器
      playerContainer.innerHTML = '';

      const token = await tokenManager.getHikToken();
      const videoUrl = `ezopen://open.ys7.com/${deviceSerial}/${channelNo}.live`;

      // 获取容器尺寸
      const { width: containerWidth, height: containerHeight } = playerContainer.getBoundingClientRect();

      const playerConfig = {
        staticPath: "/ezuikit_static/v65",
        id: playerId,
        accessToken: token,
        url: videoUrl,
        quality: 6,
        audio: false,
        themeData: this.ezuikitTheme,
        useHardDev: false,
        width: 870,
        height: 300,
      };

      console.log(`[LocalPlayerManager] 初始化播放器: ${playerId}`);

      const player = await safeInitEZUIKit(playerConfig);
      if (player) {
        this.players.set(playerId, {
          instance: player,
          type: type,
          data,
          onLoadingChange,
          onError
        });

        const visibilityCleanup = setupEZUIKitVisibilityHandler ? setupEZUIKitVisibilityHandler(player) : null;
        if (visibilityCleanup) {
          this.visibilityHandlers.set(playerId, visibilityCleanup);
        }

        console.log(`[LocalPlayerManager] 播放器 ${playerId} 初始化成功`);
      }
    } catch (error) {
      console.error(`[LocalPlayerManager] 播放器 ${playerId} 初始化失败:`, error);
      this.setErrorState(playerId, error.message);
      if (onError) onError(playerId, error);
    } finally {
      this.initializingPlayers.delete(playerId);
      this.setLoadingState(playerId, false);
      if (onLoadingChange) onLoadingChange(playerId, false);
    }
  }

  // 安全地销毁播放器
  async destroyPlayer(playerId) {
    if (!this.players.has(playerId)) return;
    
    // 如果正在销毁，不重复操作
    if (this.destroyingPlayers.has(playerId)) {
      console.log(`[LocalPlayerManager] 播放器 ${playerId} 已在销毁中`);
      return;
    }

    this.destroyingPlayers.add(playerId);

    const playerInfo = this.players.get(playerId);
    console.log(`[LocalPlayerManager] 开始销毁播放器: ${playerId}`);

    try {
      // 清理可见性处理器
      const visibilityHandler = this.visibilityHandlers.get(playerId);
      if (visibilityHandler) {
        visibilityHandler();
        this.visibilityHandlers.delete(playerId);
      }

      // 销毁播放器实例
      if (playerInfo && playerInfo.instance) {
        await safeDestroyEZUIKit(playerInfo.instance);
      }

      // 清理记录
      this.players.delete(playerId);
      this.loadingStates.delete(playerId);
      this.errorStates.delete(playerId);

      console.log(`[LocalPlayerManager] 播放器 ${playerId} 销毁完成`);
    } catch (error) {
      console.error(`[LocalPlayerManager] 销毁播放器 ${playerId} 失败:`, error);
    } finally {
      this.destroyingPlayers.delete(playerId);
    }
  }

  // 停止播放器但不销毁
  async stopPlayer(playerId) {
    const playerInfo = this.players.get(playerId);
    if (!playerInfo || !playerInfo.instance) return;

    try {
      if (playerInfo.instance.stop) {
        playerInfo.instance.stop();
        console.log(`[LocalPlayerManager] 播放器 ${playerId} 已停止`);
      }
    } catch (error) {
      console.error(`[LocalPlayerManager] 停止播放器 ${playerId} 失败:`, error);
    }
  }

  // 恢复播放器
  async playPlayer(playerId) {
    const playerInfo = this.players.get(playerId);
    if (!playerInfo || !playerInfo.instance) return;

    try {
      if (playerInfo.instance.play) {
        playerInfo.instance.play();
        console.log(`[LocalPlayerManager] 播放器 ${playerId} 已恢复播放`);
      }
    } catch (error) {
      console.error(`[LocalPlayerManager] 恢复播放器 ${playerId} 失败:`, error);
    }
  }

  // 销毁所有播放器
  async destroyAllPlayers() {
    const playerIds = Array.from(this.players.keys());
    console.log(`[LocalPlayerManager] 准备销毁所有 ${playerIds.length} 个播放器`);
    
    // 并行销毁所有播放器
    await Promise.all(playerIds.map(id => this.destroyPlayer(id)));
    
    console.log(`[LocalPlayerManager] 所有播放器已销毁`);
  }

  // 状态管理方法
  setLoadingState(playerId, loading) {
    this.loadingStates.set(playerId, loading);
  }

  getLoadingState(playerId) {
    return this.loadingStates.get(playerId) || false;
  }

  setErrorState(playerId, error) {
    this.errorStates.set(playerId, error);
  }

  getErrorState(playerId) {
    return this.errorStates.get(playerId) || null;
  }

  clearErrorState(playerId) {
    this.errorStates.delete(playerId);
  }

  hasPlayer(playerId) {
    return this.players.has(playerId);
  }

  isInitializing(playerId) {
    return this.initializingPlayers.has(playerId);
  }

  isDestroying(playerId) {
    return this.destroyingPlayers.has(playerId);
  }
}

// 创建本地播放器管理器实例
const localPlayerManager = new LocalPlayerManager();

// 设备信息相关变量
const deviceList = ref([]);
const selectedDeviceId = ref('');
const deviceTypeMap = {
  0: "1003001", // 环境监测
  1: "1003002", // 塔吊
  2: "1003003", // 升降机
  3: "1003004"  // 卸料平台
};

const projectName = ref('');
const title = computed(() => projectName.value || t('projectDetail.overview.projectTitle'));
const activeTab = ref(0);
const tabs = computed(() => [
  t('projectDetail.equipmentFacilities.tabs.generalDetection'),
  t('projectDetail.equipmentFacilities.tabs.towerCraneMonitoring'),
  t('projectDetail.equipmentFacilities.tabs.elevatorMonitoring'),
  t('projectDetail.equipmentFacilities.tabs.unloadingPlatform')
]);

// ========== 监控相关变量 ==========
const monitorList = ref([]);
const selectedMonitorId = ref("");
const currentMonitor = ref(null);
const videoBox = ref(null);
const videoPlaceholderText = ref("请选择摄像头");
const isSwitchingCamera = ref(false);

// 扬尘数据
const dustData = ref([
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pm25'), value: "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.microgram') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pm10'), value: "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.microgram') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.temperature'), value: "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.celsius') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.humidity'), value: "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.percent') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.windSpeed'), value: "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.meterPerSecond') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.windDirection'), value: "--", unit: "" },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.noise'), value: "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.decibel') },
  { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pressure'), value: "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.kilopascal') }
]);

// 塔吊统计数据
const towerCraneStats = ref({
  tdCount: 0,
  tdStatusCount: 0,
  offStatusCount: 0,
  todayWarnCount: 0,
  todayAlarmCount: 0,
  todayZzCount: 0
});

// 选中的塔吊设备详细信息
const selectedTowerCraneInfo = ref(null);

// 升降机统计数据
const elevatorStats = ref({
  tdCount: 0,
  tdStatusCount: 0,
  offStatusCount: 0,
  todayWarnCount: 0,
  todayAlarmCount: 0,
  todayZzCount: 0
});

// 选中的升降机设备详细信息
const selectedElevatorInfo = ref(null);

// 卸料平台统计数据
const unloadingStats = ref({
  tdCount: 0,
  tdStatusCount: 0,
  offStatusCount: 0,
  todayWarnCount: 0,
  todayAlarmCount: 0,
  todayZzCount: 0
});

// 选中的卸料平台设备详细信息
const selectedUnloadingInfo = ref(null);

// ========== 监控播放器相关方法 ==========
function getPlayerId(monitor) {
  if (!monitor) return '';
  const deviceSerial = monitor.nvrSerialNo || monitor.cameraSn;
  const channelNo = monitor.cameraChannel || '1';
  return `monitor-player-${deviceSerial}-${channelNo}`;
}

function getStatusText(status) {
  switch (status) {
    case '1': return '在线';
    case '0': return '离线';
    case '-1': return '异常';
    default: return '未知';
  }
}

// 初始化播放器
const initializeMonitorPlayer = async (monitor) => {
  try {
    console.log('[LocalPlayerManager] 初始化播放器:', monitor);
    
    await localPlayerManager.initPlayer({
      data: monitor,
      type: 'monitor',
      onLoadingChange: (playerId, isLoading) => {
        console.log('[LocalPlayerManager] 加载状态变化:', playerId, isLoading);
      },
      onError: (playerId, error) => {
        console.error('[LocalPlayerManager] 播放器错误:', playerId, error);
        videoPlaceholderText.value = `播放器错误: ${error.message}`;
      }
    });
  } catch (error) {
    console.error('[LocalPlayerManager] 初始化播放器失败:', error);
    videoPlaceholderText.value = `播放器初始化失败: ${error.message}`;
  }
};

// 切换摄像头的防抖处理
let cameraSwitchTimer = null;
const debouncedCameraSwitch = async (newMonitorId) => {
  if (cameraSwitchTimer) {
    clearTimeout(cameraSwitchTimer);
  }
  
  return new Promise((resolve) => {
    cameraSwitchTimer = setTimeout(async () => {
      await handleCameraSwitch(newMonitorId);
      resolve();
    }, 300);
  });
};

// 处理摄像头切换
const handleCameraSwitch = async (newMonitorId) => {
  console.log('[LocalPlayerManager] 开始切换摄像头:', newMonitorId);
  isSwitchingCamera.value = true;
  
  try {
    // 找到新的摄像头
    const newMonitor = monitorList.value.find(m => m.cameraId === newMonitorId);
    if (!newMonitor) {
      console.warn('[LocalPlayerManager] 未找到摄像头:', newMonitorId);
      return;
    }
    
    // 更新当前摄像头
    currentMonitor.value = newMonitor;
    
    // 如果是离线摄像头，不初始化播放器
    if (newMonitor.cameraStatus !== '1') {
      videoPlaceholderText.value = `摄像头 [${newMonitor.cameraName || '未命名'}] 离线`;
      return;
    }
    
    // 停止其他播放器
    for (const monitor of monitorList.value) {
      if (monitor.cameraId !== newMonitorId) {
        const playerId = getPlayerId(monitor);
        if (localPlayerManager.hasPlayer(playerId)) {
          await localPlayerManager.destroyAllPlayers();
        }
      }
    }
    
    // 等待DOM更新
    await nextTick();
    
    // 初始化或恢复当前播放器
    const currentPlayerId = getPlayerId(newMonitor);
    if (localPlayerManager.hasPlayer(currentPlayerId)) {
      // 如果播放器已存在，恢复播放
      await localPlayerManager.playPlayer(currentPlayerId);
    } else {
      // 否则初始化新播放器
      await initializeMonitorPlayer(newMonitor);
    }
    
    videoPlaceholderText.value = "";
  } catch (error) {
    console.error('[LocalPlayerManager] 切换摄像头失败:', error);
    videoPlaceholderText.value = "切换摄像头失败";
  } finally {
    isSwitchingCamera.value = false;
  }
};

// 全屏功能
const toggleFullScreen = () => {
  if (videoBox.value) {
    if (!document.fullscreenElement) {
      videoBox.value.requestFullscreen().catch(err => {
        console.log(`全屏请求被拒绝：${err.message}`);
      });
    } else {
      document.exitFullscreen();
    }
  }
};

// 格式化项目数据
const formatProjectData = (data) => {
  if (data) {
    projectName.value = data.项目名称 || data.name || '';
  }
};

// 获取设备信息方法
async function getDeviceInfo(deviceType) {
  try {
    if (!props.projectData || !props.projectData.code) {
      console.warn("项目编号不存在，无法获取设备信息");
      return;
    }
    
    const response = await request({
      url: "/globalManage/deviceMonitor/getDeviceByCode",
      method: "post",
      data: {
        projectCode: props.projectData.code,
        deviceType: deviceType
      }
    });
    
    if (response.code === 0 && response.data) {
      deviceList.value = response.data;
      console.log(`设备类型 ${deviceType} 数据获取成功:`, deviceList.value);
      
      if (deviceList.value.length > 0 && !selectedDeviceId.value) {
        selectedDeviceId.value = deviceList.value[0].deviceId;
      }
    } else {
      console.warn(`获取设备类型 ${deviceType} 数据失败:`, response.msg);
      deviceList.value = [];
    }
  } catch (error) {
    console.error("获取设备信息出错:", error);
    deviceList.value = [];
  }
}

// ========== 获取监控摄像头列表 ==========
async function getMonitorsByProjectCode() {
  console.log("[LocalPlayerManager] 开始获取监控设备列表...");
  
  // 清理数据但不销毁播放器（保留已初始化的播放器）
  monitorList.value = [];
  selectedMonitorId.value = '';
  currentMonitor.value = null;
  videoPlaceholderText.value = "正在获取摄像头列表...";

  if (!props.projectData?.code) {
    videoPlaceholderText.value = "暂无可用摄像头";
    console.warn("项目信息不完整，无法获取摄像头");
    return;
  }
  
  try {
    const response = await request({
      url: "/globalManage/deviceMonitor/getCameraByCode",
      method: "post",
      data: {
        projectCode: props.projectData.code
      }
    });
    
    console.log("摄像头列表接口返回:", response);
    if (response.code === 0 && Array.isArray(response.data)) {
      monitorList.value = response.data;
      console.log('[LocalPlayerManager] 摄像头列表数据:', monitorList.value);
      
      if (monitorList.value.length > 0) {
        console.log(`[LocalPlayerManager] 获取到 ${monitorList.value.length} 个摄像头`);
        
        // 等待DOM更新
        await nextTick();
        
        // 选择第一个在线的摄像头，如果没有在线的就选第一个
        const onlineMonitor = monitorList.value.find(monitor => monitor.cameraStatus === '1');
        const targetMonitor = onlineMonitor || monitorList.value[0];
        
        console.log('[LocalPlayerManager] 准备选择的摄像头:', targetMonitor);
        
        // 设置选中的摄像头ID
        selectedMonitorId.value = targetMonitor.cameraId;
        
        console.log('[LocalPlayerManager] 已设置选中的摄像头ID:', selectedMonitorId.value);
      } else {
        console.log("该项目下无摄像头");
        videoPlaceholderText.value = "暂无可用摄像头";
      }
    } else {
      videoPlaceholderText.value = "暂无可用摄像头";
      console.warn("获取项目摄像头信息失败:", response.msg || '未知错误');
    }
  } catch (error) {
    videoPlaceholderText.value = "暂无可用摄像头";
    console.error("获取项目摄像头信息出错:", error);
  }
}

// 获取设备详情
async function getDeviceDetail(deviceId) {
  if (!deviceId) {
    console.warn("设备ID不存在，无法获取设备详情");
    return null;
  }
  
  try {
    const response = await request({
      url: "/globalManage/zjmanage/device/getDeviceDetailsById",
      method: "post",
      data: {
        deviceId: deviceId
      }
    });
    
    if (response.code === 0 && response.data) {
      console.log("获取设备详情成功:", response.data);
      
      if (activeTab.value === 0) {
        if (response.data.device && response.data.device.deviceSn) {
          getEnvironmentData(response.data.device.deviceSn);
        } else {
          console.warn("设备详情中没有deviceSn信息");
        }
      }
      
      return response.data;
    } else {
      console.warn("获取设备详情失败:", response.msg);
      return null;
    }
  } catch (error) {
    console.error("获取设备详情出错:", error);
    return null;
  }
}

// 获取环境监测数据
async function getEnvironmentData(deviceSn) {
  if (!deviceSn) {
    console.warn("设备SN不存在，无法获取环境监测数据");
    return;
  }
  
  try {
    const response = await request({
      url: "/globalManage/deviceMonitor/getEnvironmentDataBySn",
      method: "post",
      data: {
        deviceSn: deviceSn
      }
    });
    
    if (response.code === 0 && response.data) {
      console.log("获取环境监测数据成功:", response.data);
      updateDustMonitoringData(response.data);
    } else {
      console.warn("获取环境监测数据失败:", response.msg);
    }
  } catch (error) {
    console.error("获取环境监测数据出错:", error);
  }
}

// 获取塔吊设备统计信息
async function getTowerCraneStats() {
  if (!props.projectData?.code) {
    console.warn("项目编号不存在，无法获取塔吊设备统计信息");
    return;
  }
  
  try {
    const response = await request({
      url: "/globalManage/deviceMonitor/getDeviceTotal",
      method: "post",
      data: {
        projectCode: props.projectData.code,
        deviceType: "1003002"
      }
    });
    
    if (response.code === 0 && response.data) {
      console.log("获取塔吊设备统计信息成功:", response.data);
      towerCraneStats.value = response.data;
    } else {
      console.warn("获取塔吊设备统计信息失败:", response.msg);
    }
  } catch (error) {
    console.error("获取塔吊设备统计信息出错:", error);
  }
}

// 获取塔吊设备详细信息
async function getTowerCraneInfo() {
  if (!props.projectData?.code) {
    console.warn("项目编号不存在，无法获取塔吊设备信息");
    return;
  }
  
  try {
    const response = await request({
      url: "/globalManage/deviceMonitor/getTowerCraneInfo",
      method: "post",
      data: {
        projectCode: props.projectData.code
      }
    });
    
    if (response.code === 0 && response.data) {
      console.log("获取塔吊设备信息成功:", response.data);
      deviceList.value = response.data;
      
      if (deviceList.value.length > 0 && !selectedDeviceId.value) {
        selectedDeviceId.value = deviceList.value[0].deviceId;
        selectedTowerCraneInfo.value = deviceList.value[0];
      }
    } else {
      console.warn("获取塔吊设备信息失败:", response.msg);
      deviceList.value = [];
    }
  } catch (error) {
    console.error("获取塔吊设备信息出错:", error);
    deviceList.value = [];
  }
}

// 获取升降机设备统计信息
async function getElevatorStats() {
  if (!props.projectData?.code) {
    console.warn("项目编号不存在，无法获取升降机设备统计信息");
    return;
  }
  
  try {
    const response = await request({
      url: "/globalManage/deviceMonitor/getDeviceTotal",
      method: "post",
      data: {
        projectCode: props.projectData.code,
        deviceType: "1003003"
      }
    });
    
    if (response.code === 0 && response.data) {
      console.log("获取升降机设备统计信息成功:", response.data);
      elevatorStats.value = response.data;
    } else {
      console.warn("获取升降机设备统计信息失败:", response.msg);
    }
  } catch (error) {
    console.error("获取升降机设备统计信息出错:", error);
  }
}

// 获取升降机设备详细信息
async function getElevatorInfo() {
  if (!props.projectData?.code) {
    console.warn("项目编号不存在，无法获取升降机设备信息");
    return;
  }
  
  try {
    const response = await request({
      url: "/globalManage/deviceMonitor/getDeviceByCode",
      method: "post",
      data: {
        projectCode: props.projectData.code,
        deviceType: "1003003"
      }
    });
    
    if (response.code === 0 && response.data) {
      console.log("获取升降机设备信息成功:", response.data);
      deviceList.value = response.data;
      
      if (deviceList.value.length > 0 && !selectedDeviceId.value) {
        selectedDeviceId.value = deviceList.value[0].deviceId;
        selectedElevatorInfo.value = deviceList.value[0];
        
        if (deviceList.value[0].deviceSn) {
          getLiftData(deviceList.value[0].deviceSn);
        }
      }
    } else {
      console.warn("获取升降机设备信息失败:", response.msg);
      deviceList.value = [];
    }
  } catch (error) {
    console.error("获取升降机设备信息出错:", error);
    deviceList.value = [];
  }
}

// 获取卸料平台设备统计信息
async function getUnloadingStats() {
  if (!props.projectData?.code) {
    console.warn("项目编号不存在，无法获取卸料平台设备统计信息");
    return;
  }
  
  try {
    const response = await request({
      url: "/globalManage/deviceMonitor/getDeviceTotal",
      method: "post",
      data: {
        projectCode: props.projectData.code,
        deviceType: "1003004"
      }
    });
    
    if (response.code === 0 && response.data) {
      console.log("获取卸料平台设备统计信息成功:", response.data);
      unloadingStats.value = response.data;
    } else {
      console.warn("获取卸料平台设备统计信息失败:", response.msg);
    }
  } catch (error) {
    console.error("获取卸料平台设备统计信息出错:", error);
  }
}

// 获取卸料平台设备详细信息
async function getUnloadingInfo() {
  if (!props.projectData?.code) {
    console.warn("项目编号不存在，无法获取卸料平台设备信息");
    return;
  }
  
  try {
    const response = await request({
      url: "/globalManage/deviceMonitor/getDeviceByCode",
      method: "post",
      data: {
        projectCode: props.projectData.code,
        deviceType: "1003004"
      }
    });
    
    if (response.code === 0 && response.data) {
      console.log("获取卸料平台设备信息成功:", response.data);
      deviceList.value = response.data;
      
      if (deviceList.value.length > 0 && !selectedDeviceId.value) {
        selectedDeviceId.value = deviceList.value[0].deviceId;
        selectedUnloadingInfo.value = deviceList.value[0];
        
        if (deviceList.value[0].deviceSn) {
          getUnloadData(deviceList.value[0].deviceSn);
        }
      }
    } else {
      console.warn("获取卸料平台设备信息失败:", response.msg);
      deviceList.value = [];
    }
  } catch (error) {
    console.error("获取卸料平台设备信息出错:", error);
    deviceList.value = [];
  }
}

// 更新扬尘监测数据
function updateDustMonitoringData(environmentData) {
  if (!environmentData) {
    console.warn("环境监测数据不完整");
    return;
  }
  
  try {
    const newDustData = [
      { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pm25'), value: environmentData.pmTwoValue || "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.microgram') },
      { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pm10'), value: environmentData.pmTenValue || "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.microgram') },
      { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.temperature'), value: environmentData.wdValue || "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.celsius') },
      { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.humidity'), value: environmentData.sdValue || "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.percent') },
      { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.windSpeed'), value: environmentData.fsValue || "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.meterPerSecond') },
      { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.windDirection'), value: environmentData.fxValue || "--", unit: "" },
      { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.noise'), value: environmentData.laValue || "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.decibel') },
      { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pressure'), value: environmentData.dqylValue || "--", unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.kilopascal') }
    ];
    
    dustData.value = newDustData;
    console.log("扬尘监测数据已更新:", dustData.value);
  } catch (error) {
    console.error("处理扬尘数据失败:", error);
  }
}

// 加载当前选项卡所需的所有数据
async function loadTabData(tabIndex) {
  console.log('====== 开始加载选项卡数据 ======');
  console.log('选项卡索引:', tabIndex, '设备类型:', deviceTypeMap[tabIndex]);
  console.log('项目数据:', props.projectData);
  
  deviceList.value = [];
  selectedDeviceId.value = '';
  
  if (tabIndex === 0) {
    // 常规检测选项卡 - 环境监测/扬尘监测
    if (deviceTypeMap[tabIndex] && props.projectData?.code) {
      console.log('准备获取环境监测设备信息, 项目编号:', props.projectData.code);
      getDeviceInfo(deviceTypeMap[tabIndex]).then(() => {
        if (deviceList.value.length > 0) {
          if (!selectedDeviceId.value) {
            selectedDeviceId.value = deviceList.value[0].deviceId;
            console.log('自动选择第一个环境监测设备:', selectedDeviceId.value);
            
            if (deviceList.value[0].deviceSn) {
              getEnvironmentData(deviceList.value[0].deviceSn);
            } else {
              getDeviceDetail(selectedDeviceId.value);
            }
          }
        }
      });
    }
    
    // 获取项目摄像头列表
    if (props.projectData?.code) {
      console.log('[LocalPlayerManager] 开始获取摄像头列表...');
      await getMonitorsByProjectCode();
    }
  } else if (tabIndex === 1) {
    // 塔吊监测选项卡
    if (props.projectData?.code) {
      console.log('准备获取塔吊设备信息, 项目编号:', props.projectData.code);
      getTowerCraneStats();
      getTowerCraneInfo();
    }
  } else if (tabIndex === 2) {
    // 升降机监测选项卡
    if (props.projectData?.code) {
      console.log('准备获取升降机设备信息, 项目编号:', props.projectData.code);
      getElevatorStats();
      getDeviceInfo(deviceTypeMap[tabIndex]).then(() => {
        console.log('升降机设备列表获取结果:', deviceList.value);
        if (deviceList.value.length > 0) {
          selectedDeviceId.value = deviceList.value[0].deviceId;
          selectedElevatorInfo.value = deviceList.value[0];
          
          console.log('选中升降机设备:', selectedElevatorInfo.value);
          
          if (deviceList.value[0].deviceSn) {
            console.log('准备获取升降机监测数据, deviceSn:', deviceList.value[0].deviceSn);
            getLiftData(deviceList.value[0].deviceSn);
          } else {
            console.warn('升降机设备缺少deviceSn, 无法获取监测数据');
            getDeviceDetail(selectedDeviceId.value).then(deviceDetail => {
              if (deviceDetail?.device?.deviceSn) {
                console.log('通过设备详情获取到deviceSn:', deviceDetail.device.deviceSn);
                getLiftData(deviceDetail.device.deviceSn);
              }
            });
          }
        } else {
          console.warn('未获取到升降机设备数据');
        }
      });
    }
  } else if (tabIndex === 3) {
    // 卸料平台选项卡
    if (props.projectData?.code) {
      console.log('准备获取卸料平台设备信息, 项目编号:', props.projectData.code);
      getUnloadingStats();
      getDeviceInfo(deviceTypeMap[tabIndex]).then(() => {
        console.log('卸料平台设备列表获取结果:', deviceList.value);
        if (deviceList.value.length > 0) {
          selectedDeviceId.value = deviceList.value[0].deviceId;
          selectedUnloadingInfo.value = deviceList.value[0];
          
          console.log('选中卸料平台设备:', selectedUnloadingInfo.value);
          
          if (deviceList.value[0].deviceSn) {
            console.log('准备获取卸料平台监测数据, deviceSn:', deviceList.value[0].deviceSn);
            getUnloadData(deviceList.value[0].deviceSn);
          } else {
            console.warn('卸料平台设备缺少deviceSn, 无法获取监测数据');
            getDeviceDetail(selectedDeviceId.value).then(deviceDetail => {
              if (deviceDetail?.device?.deviceSn) {
                console.log('通过设备详情获取到deviceSn:', deviceDetail.device.deviceSn);
                getUnloadData(deviceDetail.device.deviceSn);
              }
            });
          }
        } else {
          console.warn('未获取到卸料平台设备数据');
        }
      });
    }
  }
  
  console.log('====== 选项卡数据加载请求已发送 ======');
}

// 根据选项卡切换获取相应数据
watch(activeTab, async (newTab, oldTab) => {
  console.log('选项卡切换:', oldTab, '->', newTab);
  
  selectedDeviceId.value = '';
  
  if (oldTab === 0) {
    // 停止所有播放器但不销毁（保留已初始化的播放器）
    for (const monitor of monitorList.value) {
      const playerId = getPlayerId(monitor);
      if (localPlayerManager.hasPlayer(playerId)) {
       await localPlayerManager.destroyAllPlayers();
      }
    }
    currentMonitor.value = null;
    monitorList.value = [];
    selectedMonitorId.value = '';
    dustData.value = dustData.value.map(item => ({...item, value: "--"}));
  } else if (oldTab === 1) {
    selectedTowerCraneInfo.value = null;
  } else if (oldTab === 2) {
    selectedElevatorInfo.value = null;
  } else if (oldTab === 3) {
    selectedUnloadingInfo.value = null;
  }
  
  await loadTabData(newTab);
});

// 监听项目数据变化
watch(
  () => props.projectData,
  async (newData) => {
    if (newData) {
      formatProjectData(newData);
      await loadTabData(activeTab.value);
    }
  },
  { immediate: true }
);

// 监听语言变化
watch(locale, async (newLocale) => {
  console.log('语言已切换到:', newLocale);
  // 更新扬尘数据的名称
  dustData.value = [
    { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pm25'), value: dustData.value[0].value, unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.microgram') },
    { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pm10'), value: dustData.value[1].value, unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.microgram') },
    { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.temperature'), value: dustData.value[2].value, unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.celsius') },
    { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.humidity'), value: dustData.value[3].value, unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.percent') },
    { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.windSpeed'), value: dustData.value[4].value, unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.meterPerSecond') },
    { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.windDirection'), value: dustData.value[5].value, unit: "" },
    { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.noise'), value: dustData.value[6].value, unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.decibel') },
    { name: t('projectDetail.equipmentFacilities.dustMonitoring.data.pressure'), value: dustData.value[7].value, unit: t('projectDetail.equipmentFacilities.dustMonitoring.units.kilopascal') }
  ];
}, { immediate: true });

// ========== 监听摄像头选择变化 ==========
watch(selectedMonitorId, async (newMonitorId, oldMonitorId) => {
  if (newMonitorId === oldMonitorId) return;
  
  console.log('[LocalPlayerManager] 摄像头选择已变更:', oldMonitorId, '->', newMonitorId);
  
  if (newMonitorId) {
    await debouncedCameraSwitch(newMonitorId);
  } else {
    currentMonitor.value = null;
    videoPlaceholderText.value = "请选择摄像头";
  }
});

// 监听设备选择变化
watch(selectedDeviceId, (newDeviceId, oldDeviceId) => {
  console.log('设备选择已变更:', oldDeviceId, '->', newDeviceId);
  
  if (newDeviceId) {
    const selectedDevice = deviceList.value.find(device => device.deviceId === newDeviceId);
    
    if (activeTab.value === 0) {
      if (selectedDevice && selectedDevice.deviceSn) {
        getEnvironmentData(selectedDevice.deviceSn);
      } else {
        getDeviceDetail(newDeviceId);
      }
    } else if (activeTab.value === 1) {
      console.log('塔吊设备选择变化，更新详细信息:', newDeviceId);
      if (selectedDevice) {
        selectedTowerCraneInfo.value = selectedDevice;
      }
    } else if (activeTab.value === 2) {
      console.log('升降机设备选择变化，更新详细信息:', newDeviceId);
      if (selectedDevice) {
        selectedElevatorInfo.value = selectedDevice;
        
        if (selectedDevice.deviceSn) {
          getLiftData(selectedDevice.deviceSn);
        }
      }
} else if (activeTab.value === 3) {
      console.log('卸料平台设备选择变化，更新详细信息:', newDeviceId);
      if (selectedDevice) {
        selectedUnloadingInfo.value = selectedDevice;
        
        if (selectedDevice.deviceSn) {
          getUnloadData(selectedDevice.deviceSn);
        }
      }
    }
  } else {
    if (activeTab.value === 0) {
      dustData.value = dustData.value.map(item => ({...item, value: "--"}));
    } else if (activeTab.value === 1) {
      selectedTowerCraneInfo.value = null;
    } else if (activeTab.value === 2) {
      selectedElevatorInfo.value = null;
    } else if (activeTab.value === 3) {
      selectedUnloadingInfo.value = null;
    }
  }
});

// 实时数据刷新定时器
let refreshTimer = null;

// 启动实时数据刷新
function startDataRefresh() {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  
  refreshTimer = setInterval(() => {
    console.log("定时刷新设备数据...");
    
    if (!selectedDeviceId.value) return;
    
    const selectedDevice = deviceList.value.find(device => device.deviceId === selectedDeviceId.value);
    if (!selectedDevice || !selectedDevice.deviceSn) return;
    
    if (activeTab.value === 0) {
      getEnvironmentData(selectedDevice.deviceSn);
    } else if (activeTab.value === 2) {
      getLiftData(selectedDevice.deviceSn);
    } else if (activeTab.value === 3) {
      getUnloadData(selectedDevice.deviceSn);
    }
  }, 30000);
}

// 停止数据刷新
function stopDataRefresh() {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
}

onMounted(async () => {
  console.log('===== item3组件已挂载 =====');
  console.log('[LocalPlayerManager] 本地播放器管理器已初始化');
  console.log('开始加载初始选项卡数据:', activeTab.value);
  
  await loadTabData(activeTab.value);
  startDataRefresh();
});

onBeforeUnmount(async () => {
  console.log('[LocalPlayerManager] 组件即将卸载，开始清理资源');
  
  try {
    // 停止数据刷新
    stopDataRefresh();
    
    // 清除定时器
    if (cameraSwitchTimer) {
      clearTimeout(cameraSwitchTimer);
      cameraSwitchTimer = null;
    }
    
    // 使用本地播放器管理器销毁所有播放器
    await localPlayerManager.destroyAllPlayers();
    
    // 清理数据
    deviceList.value = [];
    selectedDeviceId.value = '';
    monitorList.value = [];
    selectedMonitorId.value = '';
    currentMonitor.value = null;
    selectedTowerCraneInfo.value = null;
    selectedElevatorInfo.value = null;
    selectedUnloadingInfo.value = null;
    
    // 清理统计数据
    towerCraneStats.value = {
      tdCount: 0,
      tdStatusCount: 0,
      offStatusCount: 0,
      todayWarnCount: 0,
      todayAlarmCount: 0,
      todayZzCount: 0
    };
    elevatorStats.value = {
      tdCount: 0,
      tdStatusCount: 0,
      offStatusCount: 0,
      todayWarnCount: 0,
      todayAlarmCount: 0,
      todayZzCount: 0
    };
    unloadingStats.value = {
      tdCount: 0,
      tdStatusCount: 0,
      offStatusCount: 0,
      todayWarnCount: 0,
      todayAlarmCount: 0,
      todayZzCount: 0
    };
    
    if (videoBox.value) {
      videoBox.value = null;
    }
    
    console.log('[LocalPlayerManager] item3组件已卸载，资源清理完成');
  } catch (error) {
    console.error('[LocalPlayerManager] 组件卸载时清理资源出错:', error);
  }
});

// 获取升降机监测数据
async function getLiftData(deviceSn) {
  if (!deviceSn) {
    console.warn("设备SN不存在，无法获取升降机监测数据");
    return;
  }
  
  try {
    console.log('调用升降机监测接口，参数:', { deviceSn });
    
    const response = await request({
      url: "/globalManage/deviceMonitor/getLiftDataBySn",
      method: "post",
      data: {
        deviceSn: deviceSn
      }
    });
    
    console.log('升降机监测接口返回:', response);
    
    if (response.code === 0 && response.data) {
      console.log("获取升降机监测数据成功:", response.data);
      
      if (selectedElevatorInfo.value) {
        selectedElevatorInfo.value = {
          ...selectedElevatorInfo.value,
          deviceAccessVo: {
            ...selectedElevatorInfo.value.deviceAccessVo,
            weight: response.data.zzValue || '--',
            height: response.data.gdValue || '--',
            xTilt: response.data.qjxValue || '--',
            yTilt: response.data.qjyValue || '--',
            speed: response.data.yxsdValue || '--',
            floor: response.data.lcValue || '--',
            windSpeed: response.data.fsValue || '--',
            windLevel: response.data.fjValue || '--',
            personCount: response.data.yzrsValue || '--',
            driverName: response.data.sjxmValue || '--',
            frontDoorStatus: response.data.qmsStatus || '--',
            backDoorStatus: response.data.hmsStatus || '--',
            runStatus: response.data.runStatus || '--'
          }
        };
      }
    } else {
      console.warn("获取升降机监测数据失败:", response.msg || '未知错误');
    }
  } catch (error) {
    console.error("获取升降机监测数据出错:", error);
  }
}

// 获取卸料平台监测数据
async function getUnloadData(deviceSn) {
  if (!deviceSn) {
    console.warn("设备SN不存在，无法获取卸料平台监测数据");
    return;
  }
  
  try {
    console.log('调用卸料平台监测接口，参数:', { deviceSn });
    
    const response = await request({
      url: "/globalManage/deviceMonitor/getUnloadDataBySn",
      method: "post",
      data: {
        deviceSn: deviceSn
      }
    });
    
    console.log('卸料平台监测接口返回:', response);
    
    if (response.code === 0 && response.data) {
      console.log("获取卸料平台监测数据成功:", response.data);
      
      if (selectedUnloadingInfo.value) {
        selectedUnloadingInfo.value = {
          ...selectedUnloadingInfo.value,
          deviceAccessVo: {
            ...selectedUnloadingInfo.value.deviceAccessVo,
            realTimeWeight: response.data.zzValue || '--',
            ratedWeight: response.data.ratedZzValue || '--',
            xTilt: response.data.qjxValue || '--',
            yTilt: response.data.qjyValue || '--',
            warningValue: response.data.zzyjValue || '--',
            alarmValue: response.data.zzbjValue || '--',
            loadRatio: response.data.loadRatio || '--',
            deviceStatus: response.data.deviceStatus || '--'
          }
        };
      }
    } else {
      console.warn("获取卸料平台监测数据失败:", response.msg || '未知错误');
    }
  } catch (error) {
    console.error("获取卸料平台监测数据出错:", error);
  }
}
</script>

<style lang="scss" scoped>
.left-content {
  width: 100%; // 改为响应式宽度
  // max-width: 900px; // 设置最大宽度，与主页面保持一致
  height: 100%;
  padding: 80px 20px 20px 50px;
  display: flex;
  flex-direction: column;
  position: relative;
  background: linear-gradient(
    to right,
    rgba(10, 15, 26, 0.95) 0%,        // 左侧高不透明度
    rgba(10, 15, 26, 0.85) 30%,       // 30%位置保持较高不透明度
    rgba(10, 15, 26, 0.7) 60%,        // 60%位置中等不透明度
    rgba(10, 15, 26, 0.5) 80%,        // 80%位置较低不透明度
    rgba(10, 15, 26, 0.2) 95%,        // 95%位置很低不透明度
    transparent 100%                   // 右侧完全透明
  );
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  z-index: 1;
  overflow-y: auto;

  .left-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    z-index: 2;
    margin-bottom: 10px;
  }

  .project-title {
    width: 100%; // 改为响应式宽度
    max-width: 400px; // 设置最大宽度
    height: 60px;
    background: url("@/assets/images/map/title.png") no-repeat;
    background-size: 100% 100%;
    font-family: PangMenZhengDao, PangMenZhengDao;
    font-weight: 400;
    font-size: 20px;
    color: #F2F3FF;
    line-height: 24px;
    letter-spacing: 1px;
    text-shadow: 0px 0px 10px #0094FF;
    text-align: left;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    padding-left: 30px;
    margin-bottom: 20px;
    flex-shrink: 0; // 防止被压缩
  }

  .tabs {
    display: flex;
    position: relative;
    z-index: 2;
    width: 100%; // 改为响应式宽度
    // max-width: 440px; // 设置最大宽度
    flex-wrap: wrap;
    background: rgba(0, 102, 255, 0.3);
    border-radius: 49px 49px 49px 49px;
    border-image: linear-gradient(360deg, rgba(115, 173, 249, 1), rgba(115, 173, 249, 0)) 1 1;

    .tab-item {
      flex: 1; // 改为弹性布局
      min-width: 100px; // 设置最小宽度
      height: 40px;
      line-height: 40px;
      text-align: center;
      color: #a3d2eb;
      cursor: pointer;

      &.active {
        color: #ffffff;
        background: radial-gradient(80% 80% at -18% 93%, #E5F5FF 0%, rgba(229, 245, 255, 0) 100%), rgba(0, 102, 255, 0.85);
        box-shadow: 0 2px 10px rgba(229, 245, 255, 0.3);
        border-radius: 41px 41px 41px 41px;
        border-image: linear-gradient(90deg, rgba(243, 247, 255, 0), rgba(243, 247, 255, 1), rgba(243, 247, 255, 0)) 1 1;
      }
    }
  }

  .tab-content {
    position: relative;
    z-index: 2;
    flex: 1;
    width: 100%; // 改为响应式宽度
    // max-width: 500px; // 设置最大宽度，给内容更多空间

    .content-item {
      height: 100%;
      display: flex;
      flex-direction: column;
      width: 100%; // 确保子元素也是响应式
    }

    .placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      color: #a3d2eb;
    }

    .monitor-section,
    .dust-section {
      width: 100%; // 改为响应式宽度
      // max-width: 500px; // 设置最大宽度
    }

    .monitor-control {
      display: flex;
      justify-content: space-between;
      align-items: center; // 添加垂直对齐
      margin-bottom: 15px;
      width: 100%; // 改为响应式宽度
      gap: 15px; // 添加间距

      .selector {
        flex: 1; // 改为弹性布局
        min-width: 150px; // 设置最小宽度
        // max-width: 200px; // 设置最大宽度
      }

      .control-icons {
        display: flex;
        gap: 15px;
        flex-shrink: 0; // 防止图标被压缩

        .icon-item {
          width: 40px;
          height: 40px;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;

          &:hover {
            transform: scale(1.1);
          }

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .video-container {
      width: 100%; // 改为响应式宽度
      height: 300px;
      background: #0c1018;
      border: 1px solid #005CD3;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      position: relative;

      .video-player {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #000;

        .player-wrapper {
          width: 100%;
          height: 100%;
          position: relative;
        }

        .monitor-player {
          width: 100%;
          height: 100%;
        }
      }

      .video-placeholder {
        color: #a3d2eb;
        font-size: 20px;
        text-align: center;
      }
    }

    .dust-data {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-bottom: 15px;
      width: 100%; // 改为响应式宽度
      // max-width: 500px; // 设置最大宽度
      height: 260px;
      background: url("@/assets/images/map/fourBg.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;

      .dust-row {
        display: flex;
        justify-content: space-between;

        .dust-item {
          width: 100px;
          height: 100px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .dust-value {
            height: 30px;
            width: 100%;
            font-size: 22px;
            line-height: 0px;
            color: #a3d2eb;
            text-align: center;
            font-size: 22px;
            color: #a3d2eb;
            font-family: 'oswald-medium', sans-serif;
            margin-bottom: 5px;
            background: url("@/assets/images/map/base.png") no-repeat;
            background-size: 100% 100%;
          }

          .dust-name {
            font-family: PangMenZhengDao, PangMenZhengDao;
            font-weight: 400;
            font-size: 18px;
            color: #F3F7FF;
            line-height: 21px;
            text-align: center;
            font-style: normal;
            text-transform: none;
          }
        }
      }
    }

    .dust-chart-bg {
      width: 100%; // 改为响应式宽度
      // max-width: 500px; // 设置最大宽度
      height: 100px;
      background: url("@/assets/images/map/fourBg.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    // 塔吊监测样式
    .tower-stats {
      width: 100%; // 改为响应式宽度
      // max-width: 550px; // 设置最大宽度
      height: 120px;
      display: flex;
      justify-content: center;
      padding: 0 20px;
      margin-top: 120px;
      margin-bottom: 30px;
      background: url("@/assets/images/map/towerCraneBase.png") no-repeat;
      background-size: 100% 100%;

      .tower-stat-item-1 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/total.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;
        margin-right: 20px;

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }

      .tower-stat-item-2 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/online.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;
        margin-right: 20px;

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }

      .tower-stat-item-3 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/earlyWarning.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;
        margin-right: 20px;

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }

      .tower-stat-item-4 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/warning.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }

      .tower-stat-item-5 {
        width: 90px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: url("@/assets/images/map/offline.png") no-repeat;
        background-size: 100% 100%;
        margin-top: -30px;
        margin-right: 20px;

        .stat-string {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 12px;
          color: rgba(219, 233, 255, 0.8);
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .stat-number {
          font-family: 'TCloudNumber';
          font-size: 22px;
          font-weight: bold;
          color: #a3d2eb;
          line-height: 40px;
          text-align: center;
        }

        .stat-title {
          font-weight: 400;
          font-size: 14px;
          color: #F3F7FF;
          text-align: center;
          margin-bottom: 120px;
        }
      }
    }

    .tower-details {
      width: 100%; // 改为响应式宽度
      // max-width: 550px; // 设置最大宽度
      display: flex;
      justify-content: space-between;
      gap: 20px;

      .tower-safety {
        flex: 1;
        background: url("@/assets/images/map/towerCranes.png") no-repeat;
        background-size: 100% 100%;
        padding: 20px;
        height: 260px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .tower-safety-lift {
        flex: 1;
        background: url("@/assets/images/map/towerCrane.png") no-repeat;
        background-size: 100% 100%;
        padding: 20px;
        height: 260px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .tower-safety-discharge {
        flex: 1;
        background: url("@/assets/images/map/discharge.png") no-repeat;
        background-size: 100% 100%;
        padding: 20px;
        height: 260px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .tower-params {
        flex: 1;
        background: url("@/assets/images/map/towerBg.png") no-repeat;
        background-size: 100% 100%;
        padding: 20px;
        height: 260px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding-left: 100px;
      }

      .tower-params {
        .param-item {
          font-family: PangMenZhengDao, PangMenZhengDao;
          font-size: 16px;
          color: #a3d2eb;
          line-height: 36px;
          letter-spacing: 1px;

          span {
            font-family: Alibaba PuHuiTi !important;
            font-weight: normal;
            font-size: 16px;
            color: #DBE9FF;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }

          .title-num {
            font-family: TCloudNumber, TCloudNumber;
            font-weight: 400;
            font-size: 20px;
            color: #79BFFF;
            line-height: 28px;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }
        }
      }

      .tower-safety {
        .safety-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          .safety-title {
            font-family: PangMenZhengDao, PangMenZhengDao;
            font-size: 18px;
            color: #F3F7FF;
            margin-bottom: 15px;
          }

          .safety-value {
            font-family: 'oswald-medium', sans-serif;
            font-size: 24px;
            color: #00FF00;
          }
        }
      }
    }
  }

  .info-section {
    width: 100%; // 改为响应式宽度
    max-width: 400px; // 与item1、item2保持一致
    height: 45px; // 与item1、item2保持一致
    margin: 15px 0 20px 0; // 与item1、item2保持一致
    flex-shrink: 0; // 防止标题被压缩

    .info-section-bg {
      width: 100%; // 改为响应式宽度
      height: 45px; // 与item1、item2保持一致
      background: url("@/assets/images/map/projectInfo.png") no-repeat;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      padding-left: 35px; // 与item1、item2保持一致
      font-family: PangMenZhengDao, PangMenZhengDao;
      font-size: 16px; // 与item1、item2保持一致
      color: #FFFFFF;
      line-height: 1.2; // 与item1、item2保持一致
      letter-spacing: 1px; // 与item1、item2保持一致
      text-align: left; // 与item1、item2保持一致
      font-style: normal;
      text-transform: none;
      white-space: nowrap; // 防止标题换行
      overflow: hidden; // 隐藏溢出内容
      text-overflow: ellipsis; // 显示省略号
    }
  }
}

// 视频状态指示器
.video-status-indicator {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 10;
  
  span {
    color: #F56C6C;
    &.online {
      color: #67C23A;
    }
  }
}

/* 播放器状态样式 */
.player-loading-overlay,
.player-switching-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 20;
  border-radius: 4px;
}

.player-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(40, 44, 52, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 20;
  border-radius: 4px;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.loading-spinner-small {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00FFFF;
  animation: spin 1s ease-in-out infinite;
}

.loading-text-small {
  color: #00FFFF;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
  text-align: center;
}

.error-text-small {
  color: #FF6B6B;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
  text-align: center;
}

.error-icon {
  font-size: 30px;
  opacity: 0.8;
  margin-bottom: 5px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 设备选择器提示
.select-device-hint {
  color: rgba(163, 210, 235, 0.8);
  font-size: 14px;
  padding: 10px;
  text-align: center;
}

// 全局样式调整
:deep(.el-select .el-input__inner) {
  background-color: rgba(12, 16, 24, 0.7);
  border: 1px solid rgba(163, 210, 235, 0.5);
  color: #a3d2eb;
}

:deep(.el-select .el-select__popper) {
  background-color: rgba(12, 16, 24, 0.9);
  border: 1px solid rgba(163, 210, 235, 0.5);
}

:deep(.el-select-dropdown__item) {
  color: #a3d2eb;

  &.selected,
  &.hover {
    background-color: rgba(163, 210, 235, 0.2);
  }
}

.no-video-tip {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #a3d2eb;
  font-size: 16px;
}
</style>