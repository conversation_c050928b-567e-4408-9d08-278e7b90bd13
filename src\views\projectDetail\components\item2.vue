<template>
  <div class="left-content">
    <!-- 上半部分：安全隐患 -->
    <div class="left-item one">
      <!-- 第一行：项目标题 -->
      <!-- <div class="project-title">
        {{ title }}
      </div> -->

      <!-- 第二行：标题栏 -->
      <div class="info-section">
        <div class="info-section-bg">{{ t('projectDetail.qualitySafety.safetyHazards') }}</div>
      </div>

      <!-- 第三行：排查记录和隐患记录 -->
      <div class="status-section">
        <div class="status-item">
          <svg-icon name="troubleshooting" color="#fff" style="width: 26px;height: 26px;margin-top: 3px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.inspectionRecords') }}: <span
                class="recordNum">{{
                  safetyData.排查记录 || 0 }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.types') }}</span>
            </div>
          </div>
        </div>
        <div class="status-item">
          <svg-icon name="hiddenDanger" color="#fff" style="width: 26px;height: 26px;margin-top: 3px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.hazardRecords') }}: <span
                class="recordNumRed">{{ safetyData.隐患记录 || 0 }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.types') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 第四行：已销项/待复查/待整改/超期 -->
      <div class="issue-section">
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.resolved') }}:</div>
            <div class="issue-value"> <span class="blueFont">{{ safetyData.已销项 || 0 }}</span> {{ t('projectDetail.units.projects') }}</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingReview') }}:</div>
            <div class="issue-value"> <span class="yellowFont">{{ safetyData.待复查 || 0 }}</span> {{ t('projectDetail.units.projects') }}</div>
          </div>
        </div>
        <div class="divider-line"></div>
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingRectification') }}</div>
            <div class="issue-value"> <span class="yellowFont">{{ safetyData.待整改 || 0 }}</span>{{ t('projectDetail.units.projects') }}</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.overdue') }}</div>
            <div class="issue-value"> <span class="redFont">{{ safetyData.超期 || 0 }}</span>{{ t('projectDetail.units.projects') }}</div>
          </div>
        </div>
      </div>

      <!-- 第五行：隐患数量图表 -->
      <div class="chart-section">
        <div class="chart">
          <BarChart :chartData="safetyChartData" color="255,213,0" topColor="223,192,66"></BarChart>
        </div>
      </div>

      <!-- 第二行：标题栏 -->
      <div class="info-section">
        <div class="info-section-bg">{{ t('projectDetail.qualitySafety.qualityIssues') }}</div>
      </div>

      <!-- 第三行：质量问题数据展示 - 三个指标一行 -->
      <div class="status-section three-items">
        <div class="status-item">
          <svg-icon name="hiddenDanger" color="#fff" style="width: 22px;height: 22px;margin-top: 3px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.hazardTotal') }}: <span class="recordNum"
                style="font-size: 22px;">{{ qualityData.隐患总数 || 0
                }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.count') }}</span>
            </div>
          </div>
        </div>
        <div class="status-item">
          <svg-icon name="correction" color="#fff" style="width: 22px;height: 22px;margin-top: 3px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.rectificationRate') }}: <span class="recordNum"
                style="font-size: 22px;">{{ qualityData.整改率 || 0
                }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.percent') }}</span>
            </div>
          </div>
        </div>
        <div class="status-item">
          <svg-icon name="timelyRectification" color="#fff" style="width: 22px;height: 22px;margin-top: 3px;" />
          <div class="status-content">
            <div class="status-label">{{ t('projectDetail.qualitySafety.timelyRectificationRate') }}: <span
                class="recordNum" style="font-size: 22px;">{{ qualityData.整改及时率 || 0
                }}</span>
              <span class="unit">{{ t('projectDetail.qualitySafety.units.percent') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 第四行：合格/待核验/待复查/待整改 -->
      <div class="issue-section">
        <div class="issue-bg"></div>
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.qualified') }}</div>
            <div class="issue-value "> <span class="blueFont">{{ qualityData.合格 || 0 }}</span> {{ t('projectDetail.units.projects') }}</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingVerification') }}</div>
            <div class="issue-value "> <span class="yellowFont">{{ qualityData.待核验 || 0 }}</span> {{ t('projectDetail.units.projects') }}</div>
          </div>
        </div>
        <div class="divider-line"></div>
        <div class="issue-row">
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingReview') }}</div>
            <div class="issue-value "> <span class="yellowFont">{{ qualityData.待复查 || 0 }}</span> {{ t('projectDetail.units.projects') }}</div>
          </div>
          <div class="issue-item">
            <div class="issue-label">{{ t('projectDetail.qualitySafety.status.pendingRectification') }}</div>
            <div class="issue-value "> <span class="redFont">{{ qualityData.待整改 || 0 }}</span>{{ t('projectDetail.units.projects') }}</div>
          </div>
        </div>
      </div>

      <!-- 第五行：质量问题统计图表 (组合图表) -->
      <div class="chart-section">
        <div class="chart">
          <CombinedChart 
            :barData="qualityBarData" 
            :lineData="qualityLineData" 
            :xData="qualityMonths" 
            color="255,213,0" 
            topColor="223,192,66">
          </CombinedChart>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { nextTick, ref, watch, markRaw, onMounted, computed } from "vue";
import { getImg, formatNumber } from "@/utils/method";
import * as echarts from "echarts";
import PieChart from "./PieChart.vue";
import BarChart from "./BarChart.vue";
import WaterBall from "./WaterBall.vue";
import LineChart from "./LineChart.vue";
import RotatingCircle from "@/components/RotatingCircle.vue";
import TitleBar from "@/components/TitleBar.vue";
import CombinedChart from "./CombinedChart.vue";
import axios from "axios";
import request from "@/utils/request";
import { useI18n } from 'vue-i18n';
import SvgIcon from '@/components/SvgIcon.vue';

const { t } = useI18n();

const props = defineProps({
  projectData: {
    type: Object,
    default: () => ({}),
  },
});

const title = ref(t('projectDetail.overview.projectTitle'));

// 统一数据结构 - 使用大屏端格式
const safetyData = ref({
  排查记录: 0,
  隐患记录: 0, // 统一使用大屏端字段名
  已销项: 0,
  待复查: 0,
  待整改: 0,
  超期: 0,
  "1月": "0:0.00", // 统一使用大屏端格式
  "2月": "0:0.00",
  "3月": "0:0.00",
  "4月": "0:0.00",
  "5月": "0:0.00"
});

// 统一质量数据结构
const qualityData = ref({
  整改率: "0",
  整改及时率: "0",
  合格: 0,
  待复查: 0,
  待核验: 0,
  待整改: 0,
  隐患总数: 0, // 保留PC端字段
  严重隐患: 0, // 保留PC端字段
  "1月": "0:0.00", // 统一使用大屏端格式
  "2月": "0:0.00",
  "3月": "0:0.00",
  "4月": "0:0.00",
  "5月": "0:0.00"
});

// 计算用于安全隐患图表的数据 - 兼容两种数据格式
const safetyChartData = computed(() => {
  const months = ["1月", "2月", "3月", "4月", "5月"];
  const chartData = {
    name: "隐患数量",
    data: [],
    percentages: []
  };

  months.forEach(month => {
    if (safetyData.value[month]) {
      // 兼容两种数据格式处理
      const parts = safetyData.value[month].split(':');
      let value = 0;
      let percentage = "0.00";
      
      if (parts.length === 2) {
        // 大屏端格式: "数值:百分比"
        value = parseInt(parts[0]) || 0;
        percentage = parts[1];
      } else if (parts.length === 3) {
        // PC端格式: "隐患个数:严重隐患个数:占比"
        value = parseInt(parts[0]) || 0;
        percentage = parseFloat(parts[2]).toFixed(2);
      }

      chartData.data.push(value);
      chartData.percentages.push(percentage);
    } else {
      chartData.data.push(0);
      chartData.percentages.push("0.00");
    }
  });

  return chartData;
});

// 格式化项目数据
const formatProjectData = (data) => {
  if (data) {
    title.value = data.项目名称;
  }
};

// 统一安全隐患数据获取
const fetchSafetyData = async () => {
  try {
    // 检查是否为国际版
    const isInternational = !localStorage.getItem("isChina");
    if (isInternational) {
      // 国际版使用模拟数据
      safetyData.value = {
        排查记录: 76,
        隐患记录: 25,
        已销项: 12,
        待复查: 5,
        待整改: 6,
        超期: 2,
        "1月": "5:45.00",
        "2月": "8:60.00",
        "3月": "6:55.00",
        "4月": "4:40.00",
        "5月": "2:20.00"
      };
      return;
    }

    // 国内版请求接口
    const projectInfo = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    const projectId = projectInfo.code || "";

    if (!projectId) {
      console.error("无法获取项目ID");
      return;
    }

    const response = await request.get('/globalManage/zjmanage/largescreen/getSafety', {
      params: { id: projectId },
    });

    // 统一响应处理逻辑 - 兼容两种响应格式
    let responseData;
    if (response.data && response.data.code !== undefined) {
      // 大屏端格式
      responseData = response.data;
    } else {
      // PC端格式
      responseData = response;
    }

    if (responseData.code === 0 && responseData.data) {
      const rawData = responseData.data;
      
      // 数据字段映射处理
      safetyData.value = {
        排查记录: rawData.排查记录 || 0,
        隐患记录: rawData.隐患记录 || rawData.严重隐患 || 0, // 字段名映射
        已销项: rawData.已销项 || 0,
        待复查: rawData.待复查 || 0,
        待整改: rawData.待整改 || 0,
        超期: typeof rawData.超期 === 'string' ? parseInt(rawData.超期) : (rawData.超期 || 0),
        // 月份数据保留原始格式
        ...Object.keys(rawData).filter(key => key.includes('月')).reduce((acc, key) => {
          acc[key] = rawData[key];
          return acc;
        }, {})
      };
    } else {
      console.error("获取安全隐患数据失败:", responseData.msg);
    }
  } catch (error) {
    console.error("请求安全隐患数据出错:", error);
  }
};

// 统一质量数据获取
const fetchQualityData = async () => {
  try {
    // 检查是否为国际版
    const isInternational = !localStorage.getItem("isChina");
    if (isInternational) {
      // 国际版使用模拟数据
      qualityData.value = {
        整改率: "85",
        整改及时率: "90",
        合格: 56,
        待核验: 15,
        待复查: 10,
        待整改: 8,
        隐患总数: 89,
        严重隐患: 25,
        "1月": "10:85.00",
        "2月": "12:90.00",
        "3月": "8:88.00",
        "4月": "6:92.00",
        "5月": "4:95.00"
      };
      return;
    }

    // 国内版请求接口
    const projectInfo = JSON.parse(sessionStorage.getItem("clickProject") || "{}");
    const projectId = projectInfo.code || "";

    if (!projectId) {
      console.error("无法获取项目ID");
      return;
    }

    const response = await request.get('/globalManage/zjmanage/largescreen/getQuality', {
      params: { id: projectId },
    });

    // 统一响应处理逻辑
    let responseData;
    if (response.data && response.data.code !== undefined) {
      // 大屏端格式
      responseData = response.data;
    } else {
      // PC端格式
      responseData = response;
    }

    if (responseData.code === 0 && responseData.data) {
      const rawData = responseData.data;
      
      qualityData.value = {
        整改率: typeof rawData.整改率 === 'number' ? rawData.整改率.toString() : (rawData.整改率 || "0"),
        整改及时率: typeof rawData.整改及时率 === 'number' ? rawData.整改及时率.toString() : (rawData.整改及时率 || "0"),
        合格: typeof rawData.合格 === 'string' ? parseInt(rawData.合格) : (rawData.合格 || 0),
        待核验: typeof rawData.待核验 === 'string' ? parseInt(rawData.待核验) : (rawData.待核验 || 0),
        待复查: typeof rawData.待复查 === 'string' ? parseInt(rawData.待复查) : (rawData.待复查 || 0),
        待整改: typeof rawData.待整改 === 'string' ? parseInt(rawData.待整改) : (rawData.待整改 || 0),
        隐患总数: typeof rawData.隐患总数 === 'string' ? parseInt(rawData.隐患总数) : (rawData.隐患总数 || 0),
        严重隐患: typeof rawData.严重隐患 === 'string' ? parseInt(rawData.严重隐患) : (rawData.严重隐患 || 0),
        // 月份数据保留原始格式
        ...Object.keys(rawData).filter(key => key.includes('月')).reduce((acc, key) => {
          acc[key] = rawData[key];
          return acc;
        }, {})
      };
    } else {
      console.error("获取质量数据失败:", responseData.msg);
    }
  } catch (error) {
    console.error("请求质量数据出错:", error);
  }
};

onMounted(() => {
  formatProjectData(props.projectData);
  fetchSafetyData();
  fetchQualityData();
});

watch(
  () => props.projectData,
  (newData) => {
    formatProjectData(newData);
  }
);

// 质量数据月份处理
const qualityMonths = computed(() => {
  const months = Object.keys(qualityData.value).filter(key => key.includes('月'));
  
  // 正确的月份排序逻辑
  months.sort((a, b) => {
    const monthA = parseInt(a.replace('月', ''));
    const monthB = parseInt(b.replace('月', ''));
    return monthA - monthB;
  });
  
  return months.length > 0 ? months : ["1月", "2月", "3月", "4月", "5月"];
});

const qualityBarData = computed(() => {
  const months = qualityMonths.value;
  const chartData = {
    name: "问题数量",
    data: []
  };

  months.forEach(month => {
    if (qualityData.value[month]) {
      // 兼容两种数据格式处理
      const parts = qualityData.value[month].split(':');
      let value = 0;
      
      if (parts.length === 2) {
        // 大屏端格式: "数值:百分比"
        value = parseInt(parts[0]) || 0;
      } else if (parts.length === 3) {
        // PC端格式: "问题个数:严重问题个数:占比"
        value = parseInt(parts[0]) || 0;
      }
      
      chartData.data.push(value);
    } else {
      chartData.data.push(0);
    }
  });

  return chartData;
});

const qualityLineData = computed(() => {
  const months = qualityMonths.value;
  const chartData = {
    name: "整改率",
    itemStyle: { color: "rgba(0,152,101, 1)" },
    data: []
  };

  months.forEach(month => {
    if (qualityData.value[month]) {
      // 兼容两种数据格式处理
      const parts = qualityData.value[month].split(':');
      let percentage = 0;
      
      if (parts.length === 2) {
        // 大屏端格式: "数值:百分比"
        percentage = parseFloat(parts[1]) || 0;
      } else if (parts.length === 3) {
        // PC端格式: "问题个数:严重问题个数:占比"
        percentage = parseFloat(parts[2]) || 0;
      }
      
      chartData.data.push(percentage);
    } else {
      chartData.data.push(0);
    }
  });

  return chartData;
});

// 保留原有的静态数据引用（如果其他地方需要用到）
const list1 = ref([
  {
    id: 1,
    title: "排查记录",
    num: 76,
    icon: getImg("project/icon7.png"),
  },
  {
    id: 2,
    title: "隐患记录",
    num: 6666,
    icon: getImg("project/icon8.png"),
  },
]);

const pieData1 = ref([
  {
    value: 1914,
    name: "已销项",
    itemStyle: { color: "rgba(0,255,170,1)" },
  },
  {
    value: 1601,
    name: "待复查",
    itemStyle: { color: "rgba(0,255,255, 1)" },
  },
  {
    value: 1537,
    name: "待整改",
    itemStyle: { color: "rgba(247,147,30, 1)" },
  },
  {
    value: 1007,
    name: "超期",
    itemStyle: { color: "rgba(237,28,36, 1)" },
  },
]);

const pieData2 = ref([
  {
    value: 56,
    name: "合格",
    itemStyle: { color: "rgba(0,255,170,1)" },
  },
  {
    value: 79,
    name: "待核验",
    itemStyle: { color: "rgba(0,255,255, 1)" },
  },
  {
    value: 49,
    name: "待复查",
    itemStyle: { color: "rgba(247,147,30, 1)" },
  },
  {
    value: 27,
    name: "待整改",
    itemStyle: { color: "rgba(237,28,36, 1)" },
  },
]);

const legend = ref(["折旧金额"]);
const xData = ref(["1月", "2月", "3月", "4月", "5月"]);
const chartDate = ref([
  {
    name: "折旧金额",
    itemStyle: { color: "rgba(5,85,163, 1)" },
    data: [60, 85, 56, 33, 20],
  },
]);
</script>

<style lang="scss" scoped>
.left-content {
  width: 100%;
  height: 100%;
  padding: 80px 20px 20px 50px;
  display: flex;
  flex-direction: column;
  gap: 15.6px;
  position: relative;
  background: linear-gradient(
    to right,
    rgba(10, 15, 26, 0.95) 0%,        // 左侧高不透明度
    rgba(10, 15, 26, 0.85) 30%,       // 30%位置保持较高不透明度
    rgba(10, 15, 26, 0.7) 60%,        // 60%位置中等不透明度
    rgba(10, 15, 26, 0.5) 80%,        // 80%位置较低不透明度
    rgba(10, 15, 26, 0.2) 95%,        // 95%位置很低不透明度
    transparent 100%                   // 右侧完全透明
  );
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  z-index: 1;
  overflow-y: auto;
  
  // 隐藏滚动条
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }

  .left-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    z-index: 2;
    margin-bottom: 12px;
  }
}

/* 项目标题样式 - 恢复显示 */
.project-title {
  width: 100%;
      max-width: 100%; // PC端适应容器宽度
  height: 93.6px;
  background: url("@/assets/images/map/title.png") no-repeat;
  background-size: 100% 100%;
  font-family: PangMenZhengDao, PangMenZhengDao;
  font-weight: 400;
  font-size: 20px; // PC端字体大小调整
  color: #F2F3FF;
  line-height: 24px; // PC端行高调整
  letter-spacing: 1px; // PC端字间距调整
  text-shadow: 0px 0px 13px #0094FF;
  text-align: left;
  font-style: normal;
  text-transform: none;
  display: flex;
  align-items: center;
  padding-left: 46.8px;
  margin-bottom: 20px;
}

/* 信息部分标题样式 */
.info-section {
  width: 100%;
  max-width: 400px; // 与item1保持一致
  height: 45px; // 与item1保持一致
  margin: 15px 0 20px 0; // 减少间距，与item1保持一致
  flex-shrink: 0; // 防止标题被压缩

  .info-section-bg {
    width: 100%;
    height: 45px; // 与item1保持一致
    background: url("@/assets/images/map/projectInfo.png") no-repeat;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 35px; // 与item1保持一致
    font-family: PangMenZhengDao, PangMenZhengDao;
    font-size: 16px; // 与item1保持一致
    color: #FFFFFF;
    line-height: 1.2; // 与item1保持一致
    letter-spacing: 1px; // 与item1保持一致
    text-align: left; // 与item1保持一致
    font-style: normal;
    text-transform: none;
    white-space: nowrap; // 防止标题换行
    overflow: hidden; // 隐藏溢出内容
    text-overflow: ellipsis; // 显示省略号
  }
}

/* 状态信息样式 */
.status-section {
  width: 100%;
  height: 65.52px;
  display: flex;
  margin-bottom: 31.2px;
  background: rgba(12,32,77,0.24);
  box-shadow: inset 0px 0px 6px 0px #4AA8FC, inset 0px 0px 3px 0px #4AA8FC;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(38, 85, 150, 1), rgba(12, 27, 48, 1)) 1 1; 

  &.three-items {
    .status-item {
      width: 33.33%;
      
      .status-content {
        .status-label {
          font-size: 18px; // 适应三个指标的字体大小
          line-height: 22px;
        }
      }
    }
  }

  .status-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50%;

    .status-icon {
      width: 96px;
      height: 96px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .status-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 15.6px;

      .status-label {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 14px; // PC端字体大小调整
        color: #DBE9FF;
        line-height: 18px; // PC端行高调整
        text-align: center;
        font-style: normal;
        text-transform: none;
        white-space: nowrap;
      }

      .status-value {
        display: flex;
        align-items: baseline;
        font-family: TCloudNumber;
        font-size: 28px; // PC端字体大小调整
        font-weight: bold;
        background-image: linear-gradient(to bottom,
            #9AC4FF 0%,
            #FFFFFF 62%,
            #9AC4FF 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;

        .unit {
          font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;
          font-weight: normal;
          font-size: 16px; // PC端字体大小调整
          color: rgba(219, 233, 255, 0.8);
          line-height: 31.2px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
}

/* 问题状态区域 */
.issue-section {
  width: 100%;
  height: 187.2px;
  position: relative;
  background: url("@/assets/images/map/fourBg.png") no-repeat;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  
  .issue-row {
    display: flex;
    height: 50%;
    padding: 0 31.2px;
    height: 78px;

    .issue-item {
      width: 50%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding-right: 31.2px;

      .issue-label {
        font-size: 14px; // PC端字体大小调整
        color: #ffffff;
      }
    }
  }

  .divider-line {
    width: 90%;
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
    margin: 0 auto;
  }
}

/* 图表区域 */
.chart-section {
  width: 100%;
  height: 280.8px;

  .chart-title {
    width: 100%;
    height: 31.2px;
    background: url('@/assets/images/project/sTitle.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding-left: 31.2px;
    font-size: 14px; // PC端字体大小调整
    line-height: 26.52px;
    margin-top: 15.6px;
    margin-bottom: 15.6px;
    font-weight: 300;
    color: #ffffff;
  }

  .chart {
    width: 100%;
    height: 100%;
  }
}

.recordNum {
  font-family: 'TCloudNumber';
  font-size: 22px; // PC端字体大小调整
  font-weight: bold;
  background-image: linear-gradient(to bottom,
      #9AC4FF 0%,
      #FFFFFF 62%,
      #9AC4FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.recordNumRed {
  font-family: 'TCloudNumber';
  font-size: 22px; // PC端字体大小调整
  background-image: linear-gradient(to bottom,
      #FE6263 0%,
      #FFFFFF 50%,
      #FE6263 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  font-weight: bold;
}

.blueFont {
  font-family: TCloudNumber;
  font-weight: 400;
  font-size: 20px; // PC端字体大小调整
  color: #79BFFF;
  line-height: 28px; // PC端行高调整
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.redFont {
  font-family: TCloudNumber;
  font-weight: 400;
  font-size: 20px; // PC端字体大小调整
  color: #FF6262;
  line-height: 28px; // PC端行高调整
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.yellowFont {
  font-family: TCloudNumber;
  font-weight: 400;
  font-size: 20px; // PC端字体大小调整
  color: #FCD494;
  line-height: 28px; // PC端行高调整
  text-align: right;
  font-style: normal;
  text-transform: none;
}
</style>